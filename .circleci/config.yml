version: 2.1
setup: true
orbs:
  docker: circleci/docker@2.4.0
  slack: circleci/slack@4.12.5
  gh: circleci/github-cli@2.3.0
  lp_deploy: luxurypresence/lp_deploy@1.1
  lp-dora-metrics-orb: luxurypresence/lp-dora-metrics-orb@1.0

parameters:
  pull_request:
    type: string
    description: 'Github pull request url'
    default: ''
  release:
    type: boolean
    description: 'CircleCI release trigger'
    default: false
  pr_target_branch:
    type: string
    description: 'Pull Request target branch'
    default: master
  run_e2e_test:
    type: boolean
    description: 'Condition to run e2e tests on current branch'
    default: false
  e2e_grep:
    type: string
    description: 'Grep to define which tags to run'
    default: ''
  e2e_grep_invert:
    type: string
    description: 'Grep to define which tags to not run'
    default: ''
  e2e_workers:
    type: integer
    description: 'Amount of workers to use during tests'
    default: 2
  e2e_project:
    type: string
    description: 'Which Playwright project to run'
    default: '' # empty means all projects
  updated_packages:
    type: string
    description: 'Packages updated on pull request'
    default: ''

###############################
# REFERENCES & DEFAULTS
###############################
references:
  containerConfig: &containerConfig
    docker:
      - image: cimg/node:22.15
      - image: luxurypresence/postgis-extensions-schema
        environment:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: 123123
          POSTGRES_DB: lp_test
    resource_class: medium+
    # Speeds up restore_cache as long as repo and modules fit in RAM
    working_directory: /mnt/ramdisk

###############################
# COMMANDS
###############################
commands:
  install_code:
    description: 'Checkout and install the code'
    steps:
      - checkout
      - restore_cache:
          keys:
            - v21-deps-all-{{ checksum "pnpm-lock.yaml" }}
      - run:
          name: Install pnpm package manager
          # pinning pnpm to 9.15.0 to avoid ussues with latest
          # https://github.com/pnpm/pnpm/issues/9029#issuecomment-2630882497
          command: |
            corepack enable --install-directory ~/bin
            corepack prepare pnpm@9.15.0 --activate
            pnpm config set store-dir .pnpm-store
      # Authenticate with NPM for private packages
      - run: echo "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}" > .npmrc
      - run: pnpm install --frozen-lockfile
      - save_cache:
          name: Save pnpm Package Cache
          key: v21-deps-all-{{ checksum "pnpm-lock.yaml" }}
          paths:
            - .pnpm-store
      - run:
          name: Install jq
          command: sudo apt-get update && sudo apt-get install -y jq
      - setup_remote_docker
  install_code_e2e:
    description: 'Checkout and install the code for e2e tests'
    steps:
      - checkout
      - restore_cache:
          keys:
            - v21-deps-all-{{ checksum "pnpm-lock.yaml" }}
      - run:
          name: Install pnpm package manager
          # pinning pnpm to 9.15.0 to avoid ussues with latest
          # https://github.com/pnpm/pnpm/issues/9029#issuecomment-2630882497
          command: |
            corepack enable --install-directory /bin
            corepack prepare pnpm@9.15.0 --activate
            pnpm config set store-dir .pnpm-store
      # Authenticate with NPM for private packages
      - run: echo "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}" > .npmrc
      - run: pnpm install --frozen-lockfile
      - save_cache:
          name: Save pnpm Package Cache
          key: v21-deps-all-{{ checksum "pnpm-lock.yaml" }}
          paths:
            - .pnpm-store
  get_package_version:
    description: 'Get the package version from changeset or package.json based on the branch'
    parameters:
      packageName:
        type: string
        description: 'Name of the package'
    steps:
      - run:
          name: Get package version for << parameters.packageName >>
          command: |
            if ! grep -q "<< parameters.packageName >>" changeset-status.json; then
              echo "<< parameters.packageName >> has no updates on master"
              circleci-agent step halt
            else
              VERSION=$(jq -r ".releases[] | select(.name == \"<< parameters.packageName >>\") | .newVersion" changeset-status.json)
            fi
            echo "export VERSION=$VERSION" >> $BASH_ENV
            source $BASH_ENV
            echo "Version of << parameters.packageName >>: $VERSION"
  # Expects turbo prune to be run before
  docker-build:
    description: Checks if the Docker image exists on Docker Hub and if not builds and pushes it
    parameters:
      image:
        description: Docker image name
        type: string
        default: ''
      packageName:
        description: Package name
        type: string
        default: ''
      tag:
        description: Docker image tag
        type: string
        default: ''
      buildArgs:
        description: Docker build args
        type: string
        default: ''
    steps:
      - run: cp .npmrc out/full/packages/<< parameters.packageName >>/.npmrc
      - run: mv out/full/packages/<< parameters.packageName >>/Dockerfile out/full/.
      - docker/check
      - run:
          name: Check if Docker image exists
          command: |
            if docker manifest inspect << parameters.image >>:<< parameters.tag >>; then
              echo "Image << parameters.image >>:<< parameters.tag >> already exists on Docker Hub. Skipping build."
              circleci-agent step halt
            else
              echo "Image << parameters.image >>:<< parameters.tag >> does not exist. Proceeding with build."
            fi
      - docker/build:
          image: << parameters.image >>
          dockerfile: out/full/Dockerfile
          docker-context: out/full
          tag: << parameters.tag >>
          extra_build_args: << parameters.buildArgs >>
      - docker/push:
          image: << parameters.image >>
          tag: << parameters.tag >>
  docker-build-migration:
    description: Checks if the migration Docker image exists on Docker Hub and if not builds and pushes it
    parameters:
      image:
        description: Docker image name
        type: string
        default: ''
      packageName:
        description: Package name
        type: string
        default: ''
      tag:
        description: Docker image tag
        type: string
        default: ''
      buildArgs:
        description: Docker build args
        type: string
        default: ''
    steps:
      - docker/check
      - run:
          name: Check if Docker image exists
          command: |
            if docker manifest inspect << parameters.image >>:<< parameters.tag >>; then
              echo "Image << parameters.image >>:<< parameters.tag >> already exists on Docker Hub. Skipping build."
              exit 0
            else
              echo "Image << parameters.image >>:<< parameters.tag >> does not exist. Proceeding with build."
            fi
      - docker/build:
          image: << parameters.image >>
          dockerfile: out/full/packages/<< parameters.packageName >>/migration.Dockerfile
          docker-context: out/full/packages/<< parameters.packageName >>
          tag: << parameters.tag >>,latest
          extra_build_args: << parameters.buildArgs >>
      - docker/push:
          image: << parameters.image >>
          tag: << parameters.tag >>,latest

###############################
# JOB DEFINITIONS
###############################
jobs:
  lint-sql:
    <<: *containerConfig
    steps:
      - run: |
          if [ -z << pipeline.parameters.pull_request >> ]; then
              circleci-agent step halt
          fi
      - install_code
      - run:
          name: Update Environment Variables
          command: |
            set +o pipefail
            set +e
            PULL_REQUEST_URL=<< pipeline.parameters.pull_request >>
            echo "export SQUAWK_GITHUB_PRIVATE_KEY=$(echo -e \"$SQUAWK_GITHUB_PRIVATE_KEY\")" >> "$BASH_ENV"
            echo "export SQUAWK_GITHUB_REPO_OWNER=$(echo $PULL_REQUEST_URL | awk -F/ '{print $4}')" >> "$BASH_ENV"
            echo "export SQUAWK_GITHUB_REPO_NAME=$(echo $PULL_REQUEST_URL | awk -F/ '{print $5}')" >> "$BASH_ENV"
            echo "export SQUAWK_GITHUB_PR_NUMBER=$(echo $PULL_REQUEST_URL | awk -F/ '{print $7}')" >> "$BASH_ENV"
            current_branch=$(git rev-parse --abbrev-ref HEAD)
            added_files=$(git diff --name-only --diff-filter=A master...$current_branch)
            echo "Added files before grep: $added_files"
            added_files=$(echo "$added_files" | grep -E '\.sql$' | tr '\n' ' ')
            echo "Added sql files: $added_files"
            if [ -z "$added_files" ]; then
              echo "No added SQL files found"
              circleci-agent step halt
              exit 0
            fi
            echo "export SQL_FILES=\"$(echo "$added_files")\"" >> "$BASH_ENV"
            source "$BASH_ENV"
      - run:
          name: Install Squawk
          command: npm install --prefix=$HOME/.local -g squawk-cli
      - run:
          name: Lint SQL
          command: squawk --exclude=prefer-robust-stmts upload-to-github $SQL_FILES

  lint-api-gateway:
    <<: *containerConfig
    steps:
      - when:
          condition:
            and:
              - not:
                  equal: ['', << pipeline.parameters.pull_request >>]
              - not:
                  matches:
                    {
                      pattern: '.*;api-gateway.*',
                      value: << pipeline.parameters.updated_packages >>,
                    }
          steps:
            - run:
                name: 'Skip api-gateway lint'
                command: |
                  echo "PR didn't change api-gateway package."
                  circleci-agent step halt
      - install_code
      - run:
          name: Build
          command: |
            set +e
            for _ in {1..3}; do
              pnpm build --filter=api-gateway
              if [[ $? -eq 0 ]]; then
                break
              else
                echo "Retrying build..."
              fi
            done
      - run: pnpm lint --filter=api-gateway

  lint-cms-service:
    <<: *containerConfig
    steps:
      - when:
          condition:
            and:
              - not:
                  equal: ['', << pipeline.parameters.pull_request >>]
              - not:
                  matches:
                    {
                      pattern: '.*cms-service.*',
                      value: << pipeline.parameters.updated_packages >>,
                    }
          steps:
            - run:
                name: 'Skip lint for cms-service'
                command: |
                  echo "PR didn't change cms-service"
                  circleci-agent step halt
      - install_code
      - run: pnpm lint --filter=cms-service

  lint-audit-log-service:
    <<: *containerConfig
    steps:
      - when:
          condition:
            and:
              - not:
                  equal: ['', << pipeline.parameters.pull_request >>]
              - not:
                  matches:
                    {
                      pattern: '.*audit-log-service.*',
                      value: << pipeline.parameters.updated_packages >>,
                    }
          steps:
            - run:
                name: 'Skip lint for audit-log-service'
                command: |
                  echo "PR didn't change audit-log-service"
                  circleci-agent step halt
      - install_code
      - run: pnpm build --filter=audit-log-service
      - run: pnpm lint --filter=audit-log-service

  lint-all-others:
    <<: *containerConfig
    steps:
      - when:
          condition:
            and:
              - not:
                  equal: ['', << pipeline.parameters.pull_request >>]
              - not:
                  matches:
                    {
                      pattern: '.*others.*',
                      value: << pipeline.parameters.updated_packages >>,
                    }
          steps:
            - run:
                name: 'Skip lint for packages different from api-gateway and cms-service'
                command: |
                  echo "PR didn't change packages different from api-gateway and cms-service."
                  circleci-agent step halt
      - install_code
      - run: pnpm lint --filter=\!./packages/api-gateway --filter=\!./packages/platform-tests-e2e --filter=\!./packages/local-rover --filter=\!./packages/cms-service --filter=\!./packages/audit-log-service

  test-api-gateway:
    <<: *containerConfig
    parallelism: 3
    steps:
      - when:
          condition:
            and:
              - not:
                  equal: ['', << pipeline.parameters.pull_request >>]
              - not:
                  matches:
                    {
                      pattern: '.*;api-gateway.*',
                      value: << pipeline.parameters.updated_packages >>,
                    }
          steps:
            - run:
                name: 'Skip api-gateway tests'
                command: |
                  echo "PR didn't change api-gateway package."
                  circleci-agent step halt
      - install_code
      - run:
          name: Build
          command: |
            set +e
            for _ in {1..3}; do
              pnpm build --filter=api-gateway
              if [[ $? -eq 0 ]]; then
                break
              else
                echo "Retrying build..."
              fi
            done
      - run:
          name: Java and psql for flyway
          command: |
            curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc|sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg
            echo "deb http://apt.postgresql.org/pub/repos/apt/ `lsb_release -cs`-pgdg main" |sudo tee  /etc/apt/sources.list.d/pgdg.list
            sudo apt-get update
            sudo apt install postgresql-client-16 default-jre
      - run:
          name: Get Flyway
          command: |
            wget -qO- https://download.red-gate.com/maven/release/com/redgate/flyway/flyway-commandline/10.18.0/flyway-commandline-10.18.0-linux-x64.tar.gz | tar -xvz && sudo ln -s `pwd`/flyway-10.18.0/flyway /usr/local/bin
      - run:
          name: Wait for db
          command: dockerize -wait tcp://localhost:5432 -timeout 1m
      - run:
          name: Create schema
          command: |
            psql \
              "postgresql://postgres:123123@localhost:5432/lp_test" \
              -c "CREATE SCHEMA core"
      - run: mkdir junit
      - run:
          name: Run test - Pretest includes migrations
          command: |
            cd packages/api-gateway
            pnpm test --filter=api-gateway -- --shard=$(($CIRCLE_NODE_INDEX+1))/$CIRCLE_NODE_TOTAL
            mkdir /mnt/ramdisk/all-api-reports/
            cp /mnt/ramdisk/packages/api-gateway/reports/coverage/coverage-final.json /mnt/ramdisk/all-api-reports/coverage-final-${CIRCLE_NODE_INDEX}.json
      - persist_to_workspace:
          root: .
          paths:
            - all-api-reports

  test-cms-service:
    <<: *containerConfig
    parallelism: 3
    steps:
      - when:
          condition:
            and:
              - not:
                  equal: ['', << pipeline.parameters.pull_request >>]
              - not:
                  matches:
                    {
                      pattern: '.*cms-service.*',
                      value: << pipeline.parameters.updated_packages >>,
                    }
          steps:
            - run:
                name: 'Skip cms-service tests'
                command: |
                  echo "PR didn't change cms-service package."
                  circleci-agent step halt
      - install_code
      - run:
          name: Build
          command: pnpm build --filter=cms-service
      - run:
          name: Java and psql for flyway
          command: |
            curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc|sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg
            echo "deb http://apt.postgresql.org/pub/repos/apt/ `lsb_release -cs`-pgdg main" |sudo tee  /etc/apt/sources.list.d/pgdg.list
            sudo apt-get update
            sudo apt install postgresql-client-16 default-jre
      - run:
          name: Get Flyway
          command: |
            wget -qO- https://download.red-gate.com/maven/release/com/redgate/flyway/flyway-commandline/10.18.0/flyway-commandline-10.18.0-linux-x64.tar.gz | tar -xvz && sudo ln -s `pwd`/flyway-10.18.0/flyway /usr/local/bin
      - run:
          name: Wait for db
          command: dockerize -wait tcp://localhost:5432 -timeout 1m
      - run:
          name: Create schema
          command: |
            psql \
              "postgresql://postgres:123123@localhost:5432/lp_test" \
              -c "CREATE SCHEMA property"
      - run: mkdir junit
      - run:
          name: Run regular tests - Pretest includes migrations
          command: |
            cd packages/cms-service
            pnpm test --filter=cms-service -- --shard=$(($CIRCLE_NODE_INDEX+1))/$CIRCLE_NODE_TOTAL
            mkdir -p /mnt/ramdisk/all-cms-reports/
            cp /mnt/ramdisk/packages/cms-service/reports/coverage/coverage-final.json /mnt/ramdisk/all-cms-reports/coverage-final-${CIRCLE_NODE_INDEX}.json
      - run:
          name: Run e2e tests
          command: |
            cd packages/cms-service
            # TODO: Add sharding back when there are more test suites
            pnpm test:e2e
            cp /mnt/ramdisk/packages/cms-service/reports/coverage-e2e/coverage-final.json /mnt/ramdisk/all-cms-reports/coverage-e2e-final.json
      - persist_to_workspace:
          root: .
          paths:
            - all-cms-reports

  # Test job for everything except api-gateway
  test-all-others:
    <<: *containerConfig
    steps:
      - when:
          condition:
            and:
              - not:
                  equal: ['', << pipeline.parameters.pull_request >>]
              - not:
                  matches:
                    {
                      pattern: '.*others.*',
                      value: << pipeline.parameters.updated_packages >>,
                    }
          steps:
            - run:
                name: 'Skip tests different from api-gateway and cms-service tests'
                command: |
                  echo "PR didn't changed packages different from api-gateway and cms-service."
                  circleci-agent step halt
      - install_code
      - run:
          name: Java and psql for flyway
          command: |
            curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc|sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/postgresql.gpg
            echo "deb http://apt.postgresql.org/pub/repos/apt/ `lsb_release -cs`-pgdg main" |sudo tee  /etc/apt/sources.list.d/pgdg.list
            sudo apt-get update
            sudo apt install postgresql-client-16 default-jre
      - run:
          name: Get Flyway
          command: |
            wget -qO- https://download.red-gate.com/maven/release/com/redgate/flyway/flyway-commandline/10.18.0/flyway-commandline-10.18.0-linux-x64.tar.gz | tar -xvz && sudo ln -s `pwd`/flyway-10.18.0/flyway /usr/local/bin
      - run:
          name: Wait for db
          command: dockerize -wait tcp://localhost:5432 -timeout 1m
      - run:
          name: Create schema
          command: |
            psql \
              "postgresql://postgres:123123@localhost:5432/lp_test" \
              -c "CREATE SCHEMA property"
      - run: pnpm test --filter=\!./packages/api-gateway --filter=\!./packages/platform-tests-e2e --filter=\!./packages/cms-service
      - persist_to_workspace:
          root: .
          paths:
            - packages/cms-service/reports
            - packages/web-platform/reports
      - store_artifacts:
          path: packages/cms-service/reports/coverage
          destination: cms-service-coverage

  require-changeset:
    <<: *containerConfig
    steps:
      - install_code
      - gh/setup
      - run:
          name: Determine PR conditions
          command: |
            if [[ << pipeline.parameters.pull_request >> == "" ]]; then
              echo "This job is not triggered by a GitHub PR."
              circleci-agent step halt
              exit 0
            fi

            TARGET_BRANCH="<< pipeline.parameters.pr_target_branch >>"
            echo "Target branch is $TARGET_BRANCH"

            if [[ "$TARGET_BRANCH" == "master" ]]; then
              echo "Proceeding with changeset check."
            else
              echo "Target branch is not master, skipping changeset check."
              circleci-agent step halt
            fi
      - run:
          name: Check for changeset
          command: pnpm changeset status || (echo "Changeset is missing for PR to master" && exit 1)

  release-npm-packages:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access node_modules
      - attach_workspace:
          at: .
      - run:
          name: Check if packages were modified (changeset present)
          command: |
            pnpm changeset status --output=changeset-status.json
            RELEASES_LENGTH=$(jq '.releases | length' changeset-status.json)
            if [ "$RELEASES_LENGTH" -eq "0" ]; then
              echo "no packages modified, skipping publish."
              circleci-agent step halt
            fi
      - persist_to_workspace:
          root: ./
          paths:
            - changeset-status.json
      - run:
          name: Build packages
          command: pnpm build --filter=@luxury-presence/*
      - run: git config --global user.email "<EMAIL>"
      - run: git config --global user.name "${CIRCLE_PROJECT_USERNAME}"
      - run: mkdir -p ~/.ssh
      - run: ssh-keyscan github.com >> ~/.ssh/known_hosts
      - run: pnpm publish-packages
      - run: git push origin $CIRCLE_BRANCH --follow-tags
      - slack/notify:
          event: fail
          template: basic_fail_1

  release-api-gateway:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: api-gateway
      - run: pnpm build --filter=api-gateway
      - run: pnpm turbo prune api-gateway --docker --use-gitignore=false
      - docker-build-migration:
          image: luxurypresence/load-core-migration
          packageName: api-gateway
          tag: v$VERSION
          buildArgs: --build-arg NODE_ENV=production
      - docker-build:
          image: luxurypresence/api-gateway
          packageName: api-gateway
          tag: v$VERSION
          buildArgs: --build-arg NODE_ENV=production --build-arg DD_GIT_REPOSITORY_URL=${CIRCLE_REPOSITORY_URL} --build-arg DD_GIT_COMMIT_SHA=${CIRCLE_SHA1}
      - lp-dora-metrics-orb/track_build_end:
          service: api-gateway

  release-cms-service:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: cms-service
      - run: pnpm build --filter=cms-service
      - run: pnpm turbo prune cms-service --docker --use-gitignore=false
      - docker-build-migration:
          image: luxurypresence/load-cms-migration
          packageName: cms-service
          tag: v$VERSION
          buildArgs: --build-arg NODE_ENV=production
      - docker-build:
          image: luxurypresence/cms-service
          packageName: cms-service
          tag: v$VERSION
          buildArgs: --build-arg NODE_ENV=production --build-arg DD_GIT_REPOSITORY_URL=${CIRCLE_REPOSITORY_URL} --build-arg DD_GIT_COMMIT_SHA=${CIRCLE_SHA1}
      - lp-dora-metrics-orb/track_build_end:
          service: cms-service

  release-web-platform:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: web-platform
      - run: CONFIG_PRESET=empty pnpm build --filter=web-platform
      - run: pnpm turbo prune web-platform --docker --use-gitignore=false
      - docker-build:
          image: luxurypresence/web-platform-artifacts
          packageName: web-platform
          tag: v$VERSION
      - lp-dora-metrics-orb/track_build_end:
          service: web-platform

  release-audit-log-service:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: audit-log-service
      - run: pnpm build --filter=audit-log-service
      - run: pnpm turbo prune audit-log-service --docker --use-gitignore=false
      - docker-build:
          image: luxurypresence/audit-log-service
          packageName: audit-log-service
          tag: v$VERSION
          buildArgs: --build-arg NODE_ENV=production --build-arg DD_GIT_REPOSITORY_URL=${CIRCLE_REPOSITORY_URL} --build-arg DD_GIT_COMMIT_SHA=${CIRCLE_SHA1}
      - lp-dora-metrics-orb/track_build_end:
          service: audit-log-service

  release-rss-feed-lambda:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: rss-feed-lambda
      - run: pnpm build --filter=rss-feed-lambda
      - run: pnpm turbo prune rss-feed-lambda --docker --use-gitignore=false
      - run:
          command: |
            echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/rss-feed-lambda-installer
          packageName: rss-feed-lambda
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - lp-dora-metrics-orb/track_build_end:
          service: rss-feed-lambda

  release-webhook-lambda:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: webhook-lambda
      - run: pnpm build --filter=webhook-lambda
      - run: pnpm turbo prune webhook-lambda --docker --use-gitignore=false
      - run:
          command: |
            echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/webhook-lambda-installer
          packageName: webhook-lambda
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - lp-dora-metrics-orb/track_build_end:
          service: webhook-lambda

  release-data-syndication-lambda:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: data-syndication-lambda
      - run: pnpm build --filter=data-syndication-lambda
      - run: pnpm turbo prune data-syndication-lambda --docker --use-gitignore=false
      - run: echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/data-syndication-lambda-installer
          packageName: data-syndication-lambda
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - lp-dora-metrics-orb/track_build_end:
          service: data-syndication-lambda

  release-media-lambda:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: media-lambda
      - run: pnpm build --filter=media-lambda
      - run: pnpm turbo prune media-lambda --docker --use-gitignore=false
      - run: echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/media-lambda-installer
          packageName: media-lambda
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - lp-dora-metrics-orb/track_build_end:
          service: media-lambda

  release-cmt-web-scraper-lambda:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: cmt-web-scraper-lambda
      - run: pnpm build --filter=cmt-web-scraper-lambda
      - run: pnpm turbo prune cmt-web-scraper-lambda --docker --use-gitignore=false
      - run: echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/cmt-web-scraper-lambda-installer
          packageName: cmt-web-scraper-lambda
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - lp-dora-metrics-orb/track_build_end:
          service: cmt-web-scraper-lambda

  release-dashboard-kafka-types:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - get_package_version:
          packageName: dashboard-kafka-types
      - run: pnpm build --filter=@luxury-presence/dashboard-kafka-types
      - run: cd packages/dashboard-kafka-types && node ./scripts/syncSchemaRegistry.js

  release-post-publishing-lambda:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: post-publishing-lambda
      - run: pnpm build --filter=post-publishing-lambda
      - run: pnpm turbo prune post-publishing-lambda --docker --use-gitignore=false
      - run: echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/post-publishing-lambda-installer
          packageName: post-publishing-lambda
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - lp-dora-metrics-orb/track_build_end:
          service: post-publishing-lambda

  release-property-linking-lambda:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - lp-dora-metrics-orb/track_build_start
      - get_package_version:
          packageName: property-linking-lambda
      - run: pnpm build --filter=property-linking-lambda
      - run: pnpm turbo prune property-linking-lambda --docker --use-gitignore=false
      - run: echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/property-linking-lambda-installer
          packageName: property-linking-lambda
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN
      - lp-dora-metrics-orb/track_build_end:
          service: property-linking-lambda

  release-public-api-gateway:
    <<: *containerConfig
    steps:
      - install_code
      # Attach workspace to access changeset-status.json
      - attach_workspace:
          at: .
      - get_package_version:
          packageName: public-api-gateway
      - run: pnpm build --filter=public-api-gateway
      - run: pnpm turbo prune public-api-gateway --docker --use-gitignore=false
      - run:
          command: |
            echo -e "//registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}\nnode-linker=hoisted\nsymlink=false" > .npmrc
      - run: cp .npmrc out/full/.npmrc
      - docker-build:
          image: luxurypresence/public-api-gateway
          packageName: public-api-gateway
          tag: v$VERSION
          buildArgs: --build-arg NPM_AUTH_TOKEN=$NPM_AUTH_TOKEN --build-arg DD_GIT_REPOSITORY_URL=${CIRCLE_REPOSITORY_URL} --build-arg DD_GIT_COMMIT_SHA=${CIRCLE_SHA1}
      - run:
          name: 'Deploy Swagger docs to Readme.io'
          command: |
            cd packages/public-api-gateway
            pnpm build:docs
            pnpm deploy:docs

  run-e2e-test:
    docker:
      - image: mcr.microsoft.com/playwright:v1.54.2-noble
    working_directory: /mnt/ramdisk
    resource_class: luxurypresence/eks-staging
    parallelism: 5
    steps:
      - install_code_e2e
      - run:
          name: Create report directory
          command: mkdir -p all-blob-reports
      - run:
          name: 'Run e2e tests'
          command: |
            cd packages/platform-tests-e2e
            export SLACK_REPORT=false
            export LOGS_URL="${CIRCLE_BUILD_URL}"
            export PLAYWRIGHT_REPORT_URL="https://output.circle-artifacts.com/output/job/${CIRCLE_WORKFLOW_JOB_ID}/artifacts/0/playwright-report/index.html"
            WORKERS="<< pipeline.parameters.e2e_workers >>"
            E2E_GREP="<< pipeline.parameters.e2e_grep >>"
            E2E_GREP_INVERT="<< pipeline.parameters.e2e_grep_invert >>"
            npx playwright install webkit
            npx playwright install chrome
            E2E_PROJECTS=""
            if [[ -n "<< pipeline.parameters.e2e_project >>" ]]; then
              IFS=',' read -ra PROJECTS \<<< "<< pipeline.parameters.e2e_project >>"
              for project in "${PROJECTS[@]}"; do
                E2E_PROJECTS+="--project=$project "
              done
            fi
            npx playwright test ${E2E_PROJECTS} --workers="${WORKERS}" --grep="${E2E_GREP}" --grep-invert="${E2E_GREP_INVERT}" --shard=$(($CIRCLE_NODE_INDEX+1))/$CIRCLE_NODE_TOTAL || true
            cp blob-report/* /mnt/ramdisk/all-blob-reports
      - store_artifacts:
          path: packages/platform-tests-e2e/playwright-report
          destination: playwright-report
      - store_test_results:
          path: packages/platform-tests-e2e/playwright-report/results.xml
      - persist_to_workspace:
          root: .
          paths:
            - all-blob-reports

  merge-e2e-reports:
    docker:
      - image: mcr.microsoft.com/playwright:v1.54.2-noble
    working_directory: /mnt/ramdisk
    resource_class: luxurypresence/eks-staging
    steps:
      - install_code_e2e
      - attach_workspace:
          at: .
      - run:
          name: Report
          command: |
            cd packages/platform-tests-e2e
            export SLACK_REPORT="${SLACK_REPORT}"
            export LOGS_URL="${CIRCLE_BUILD_URL}"
            export PLAYWRIGHT_REPORT_URL="https://output.circle-artifacts.com/output/job/${CIRCLE_WORKFLOW_JOB_ID}/artifacts/0/playwright-report/index.html"
            npx playwright merge-reports --config playwright.config.js /mnt/ramdisk/all-blob-reports
      - store_artifacts:
          path: packages/platform-tests-e2e/playwright-report
          destination: playwright-report

  coverage-api-gateway:
    <<: *containerConfig
    steps:
      - when:
          condition:
            and:
              - not:
                  equal: ['', << pipeline.parameters.pull_request >>]
              - not:
                  matches:
                    {
                      pattern: '.*api-gateway.*',
                      value: << pipeline.parameters.updated_packages >>,
                    }
          steps:
            - run:
                name: 'Skip api-gateway tests'
                command: |
                  echo "PR didn't changed api-gateway, skipping coverage check."
                  circleci-agent step halt
      - install_code
      - attach_workspace:
          at: .
      - run:
          name: Check coverage
          command: |
            echo "export MIN_COVERAGE=$BRONZE_COVERAGE" >> $BASH_ENV
            bash packages/api-gateway/scripts/check-coverage.sh

  coverage-cms-service:
    <<: *containerConfig
    steps:
      - when:
          condition:
            and:
              - not:
                  equal: ['', << pipeline.parameters.pull_request >>]
              - not:
                  matches:
                    {
                      pattern: '.*cms-service.*',
                      value: << pipeline.parameters.updated_packages >>,
                    }
          steps:
            - run:
                name: 'Skip cms-service tests'
                command: |
                  echo "PR didn't changed cms-service, skipping coverage check."
                  circleci-agent step halt
      - install_code
      - attach_workspace:
          at: .
      - run:
          name: Check coverage
          command: |
            echo "export MIN_COVERAGE=89" >> $BASH_ENV
            bash packages/cms-service/scripts/check-coverage.sh

###############################
# WORKFLOW DEFINITIONS
###############################
workflows:
  pull-request-audit:
    when:
      not:
        equal: ['', << pipeline.parameters.pull_request >>]
    jobs:
      - lint-api-gateway:
          context:
            - npm-read
            - slack
            - turbo-cache
      - lint-cms-service:
          context:
            - npm-read
            - slack
            - turbo-cache
      - lint-audit-log-service:
          context:
            - npm-read
            - slack
            - turbo-cache
      - lint-all-others:
          context:
            - npm-read
            - slack
            - turbo-cache
      - test-api-gateway:
          context:
            - npm-read
            - slack
            - turbo-cache
      - coverage-api-gateway:
          context:
            - npm-read
            - slack
            - turbo-cache
            - quality-gates
          requires:
            - test-api-gateway
      - test-cms-service:
          context:
            - npm-read
            - slack
            - turbo-cache
      - coverage-cms-service:
          context:
            - npm-read
            - slack
            - turbo-cache
            - quality-gates
          requires:
            - test-cms-service

      - test-all-others:
          context:
            - slack
            - npm-read
            - turbo-cache
      - lint-sql:
          context:
            - npm-read
            - squawk
      - require-changeset:
          context:
            - npm-read
            - github-creds
  test-and-release:
    when: << pipeline.parameters.release >>
    jobs:
      - lint-api-gateway:
          context:
            - npm-read
            - slack
            - turbo-cache
      - lint-cms-service:
          context:
            - npm-read
            - slack
            - turbo-cache
      - lint-audit-log-service:
          context:
            - npm-read
            - slack
            - turbo-cache
      - lint-all-others:
          context:
            - npm-read
            - slack
            - turbo-cache
      - test-api-gateway:
          context:
            - npm-read
            - slack
            - turbo-cache
      - test-cms-service:
          context:
            - npm-read
            - slack
            - turbo-cache
      - test-all-others:
          context:
            - npm-read
            - slack
            - turbo-cache
      - lint-sql:
          context:
            - npm-read
            - squawk
      - release-npm-packages:
          requires:
            - test-all-others
            - test-api-gateway
            - test-cms-service
          context:
            - npm-write
            - slack
            - turbo-cache
            - github-creds
          filters:
            branches:
              only:
                - master
      - release-api-gateway:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-cms-service:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-audit-log-service:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-web-platform:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-rss-feed-lambda:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-webhook-lambda:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-data-syndication-lambda:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-media-lambda:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-cmt-web-scraper-lambda:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-dashboard-kafka-types:
          context:
            - npm-read
            - confluent
            - slack
            - turbo-cache
          requires:
            - release-npm-packages
      - release-post-publishing-lambda:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-property-linking-lambda:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - release-public-api-gateway:
          context:
            - npm-read
            - dockerhub
            - slack
            - turbo-cache
            - lp-dora-metrics
          requires:
            - release-npm-packages
      - lp_deploy/continue:
          changeset_file: changeset-status.json
          notifications_enabled: true
          requires:
            - release-api-gateway
            - release-cms-service
            - release-audit-log-service
            - release-web-platform
            - release-rss-feed-lambda
            - release-webhook-lambda
            - release-data-syndication-lambda
            - release-media-lambda
            - release-cmt-web-scraper-lambda
            - release-dashboard-kafka-types
            - release-post-publishing-lambda
            - release-property-linking-lambda
            - release-public-api-gateway
          filters:
            branches:
              only: master # Keep this filter for security reasons.
  manual-end-to-end:
    when: << pipeline.parameters.run_e2e_test >>
    jobs:
      - run-e2e-test:
          context:
            - hackweek
            - npm-read
            - turbo-cache
      - merge-e2e-reports:
          requires:
            - run-e2e-test
          context:
            - hackweek
            - npm-read
            - turbo-cache
  nightly-end-to-end-test:
    when:
      and:
        - equal: [scheduled_pipeline, << pipeline.trigger_source >>]
        - equal: [nightly_build, << pipeline.schedule.name >>]
    jobs:
      - run-e2e-test:
          context:
            - hackweek
            - npm-read
            - turbo-cache
      - merge-e2e-reports:
          requires:
            - run-e2e-test
          context:
            - hackweek
            - npm-read
            - turbo-cache
