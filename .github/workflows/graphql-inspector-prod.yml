name: GraphQL inspector for production

on:
  push:
    branches: ['master']

jobs:
  graphql_inspector:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@master
      - name: Run GraphQL Inspector - Master
        uses: graphql-hive/graphql-inspector@v3.4.0
        with:
          schema: packages/api-gateway/src/schema.graphql
          endpoint: 'https://gw.luxurypresence.com/graphql'
