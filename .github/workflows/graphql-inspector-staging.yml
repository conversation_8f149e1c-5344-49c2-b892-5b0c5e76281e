name: GraphQL inspector for staging

on:
  pull_request:
    branches: ['master']
    types: [opened, synchronize]

jobs:
  graphql_inspector:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@master
      - name: Run GraphQL Inspector - Staging
        uses: graphql-hive/graphql-inspector@v3.4.0
        with:
          schema: packages/api-gateway/src/schema.graphql
          endpoint: 'https://gw.luxurycoders.com/graphql'
