name: Notify CircleCI on merge to master

on:
  push:
    branches: ['master']

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - name: Build CircleCI payload
        env:
          CIRCLECI_BRANCH_BASE: ${{ github.ref_name }}
        run: echo "CIRCLECI_PAYLOAD={\"release\":true, \"pr_target_branch\":\"${CIRCLECI_BRANCH_BASE}\"}" >> $GITHUB_ENV
      - name: Trigger CircleCI
        env:
          CIRCLECI_BRANCH: ${{ github.ref_name }}
          CIRCLECI_PROJECT: ${{ github.repository }}
        uses: promiseofcake/circleci-trigger-action@v1
        with:
          user-token: ${{ secrets.CIRCLECI_TOKEN }}
          project-slug: ${{ env.CIRCLECI_PROJECT }}
          branch: ${{ env.CIRCLECI_BRANCH }}
          payload: ${{ env.CIRCLECI_PAYLOAD }}
