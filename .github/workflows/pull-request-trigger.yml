name: Notify CircleCI on PR change

on:
  pull_request:
    branches: ['master']
    types: [opened, synchronize]

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get changes
        id: get_changed
        run: |
          base=${{ github.base_ref }}
          head=${{ github.head_ref }}
          echo "files=$(git diff --name-only "origin/${base}" "origin/${head}" | xargs)" >> $GITHUB_OUTPUT
      - name: Build CircleCI payload
        env:
          CIRCLECI_BRANCH_BASE: ${{ github.base_ref }}
          PULL_REQUEST_URL: ${{ github.event.pull_request.html_url }}
        run: |
          UPDATED_PACKAGES=""
          CHANGED="${{ steps.get_changed.outputs.files }}"

          # Check for api-gateway changes
          if grep -q -E -m 1 "(\/api-gateway\/)" <<< "$CHANGED"; then
            UPDATED_PACKAGES="${UPDATED_PACKAGES};api-gateway"
          fi

          # Check for cms-service changes
          if grep -q -E -m 1 "(\/cms-service\/)" <<< "$CHANGED"; then
            UPDATED_PACKAGES="${UPDATED_PACKAGES};cms-service"
          fi

          # Check for audit-log-service changes
          if grep -q -E -m 1 "(\/audit-log-service\/)" <<< "$CHANGED"; then
            UPDATED_PACKAGES="${UPDATED_PACKAGES};audit-log-service"
          fi

          # Check for changes in any package except api-gateway, cms-service, audit-log-service and platform-tests-e2e
          # Process each path individually by converting space-separated list to one path per line
          if echo "$CHANGED" | tr ' ' '\n' | grep -E "^packages\/[^\/]+\/" | grep -v -E "^packages\/(api-gateway|cms-service|audit-log-service|platform-tests-e2e)\/" | grep -q .; then
            UPDATED_PACKAGES="${UPDATED_PACKAGES};others"
          fi

          echo "CIRCLECI_PAYLOAD={\"pull_request\":\"${PULL_REQUEST_URL}\", \"updated_packages\":\"${UPDATED_PACKAGES}\", \"pr_target_branch\":\"${CIRCLECI_BRANCH_BASE}\"}" >> $GITHUB_ENV
      - name: Trigger CircleCI
        env:
          CIRCLECI_BRANCH: ${{ github.head_ref }}
          CIRCLECI_PROJECT: ${{ github.repository }}
        uses: promiseofcake/circleci-trigger-action@v1
        with:
          user-token: ${{ secrets.CIRCLECI_TOKEN }}
          project-slug: ${{ env.CIRCLECI_PROJECT }}
          branch: ${{ env.CIRCLECI_BRANCH }}
          payload: ${{ env.CIRCLECI_PAYLOAD }}
