* @luxurypresence/flagship-platform

/packages/api-gateway/src/gql/mls/ @luxurypresence/maps
/packages/api-gateway/src/gql/bss/ @luxurypresence/maps
/packages/api-gateway/src/gql/pln/ @luxurypresence/pln
/packages/api-gateway/src/services/mls/ @luxurypresence/maps
/packages/api-gateway/src/services/bss/ @luxurypresence/maps
/packages/api-gateway/src/gql/crm/ @luxurypresence/glow
/packages/api-gateway/src/services/crm/ @luxurypresence/glow
/packages/api-gateway/src/routes/v1/leads.js @luxurypresence/glow
/packages/api-gateway/src/routes/v1/hooks.js @luxurypresence/glow
/packages/api-gateway/src/routes/v1/persons.ts @luxurypresence/glow
/packages/api-gateway/src/models/Lead* @luxurypresence/glow
/packages/api-gateway/src/models/Hook.js @luxurypresence/glow
/packages/api-gateway/src/models/Integration.js @luxurypresence/glow
/packages/api-gateway/src/constants/leadConstants.js @luxurypresence/glow
/packages/api-gateway/src/routes/v1/__tests__/hooks.test.js @luxurypresence/glow
/packages/api-gateway/src/routes/v1/__tests__/leads.test.js @luxurypresence/glow
/packages/api-gateway/src/routes/v1/__tests__/leadsEndToEnd.test.js @luxurypresence/glow
/packages/api-gateway/src/routes/v1/__tests__/persons.test.ts @luxurypresence/glow
/packages/web-platform/src/scenes/Leads/ @luxurypresence/glow
/packages/web-platform/src/scenes/Lead/ @luxurypresence/glow
/packages/web-platform/src/scenes/LeadRouting/ @luxurypresence/glow
/packages/web-platform/src/scenes/Settings/Integrations/ @luxurypresence/glow
/packages/web-platform/src/scenes/Settings/Events/ @luxurypresence/glow
/packages/web-platform/src/componments/Activity*/ @luxurypresence/glow
/packages/web-platform/src/components/RegionInput/ @luxurypresence/maps
/packages/web-platform/src/utils/leadUtils.ts @luxurypresence/glow
/packages/web-platform/src/scenes/Properties/Share/ @luxurypresence/pln
/packages/web-platform/src/scenes/Properties/Private/ @luxurypresence/pln
/packages/web-platform/src/scenes/ListingNetwork/ @luxurypresence/pln
/packages/web-platform/src/scenes/FreemiumPreview/ @luxurypresence/pln

/packages/data-syndication-lambda @luxurypresence/midmarket
/packages/property-linking-lambda @luxurypresence/midmarket
/packages/public-api-gateway @luxurypresence/midmarket
/packages/rss-feed-lambda @luxurypresence/midmarket
/packages/webhook-lambda @luxurypresence/midmarket

###########
# LaunchX #
###########

/packages/api-gateway/src/gql/types/OnboardingStep.js @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/types/enums/OnboardingFormResourceEnum.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/types/CompanyOnboardingStep.js @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/types/OnboardingForm.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/sendOnboardingCompleteEmail.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/upsertOnboardingForm.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/addServicePackageToOnboarding.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/updateCompanyOnboardingStep.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/deleteServicePackageFromOnboarding.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/__tests__/updateCompanyOnboardingStep.test.js @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/__tests__/addServicePackageToOnboarding.test.js @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/__tests__/upsertOnboardingForm.test.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/__tests__/deleteServicePackageFromOnboarding.test.js @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/updateCompanyOnboardingHubState.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/types/inputs/OnboardingHubStateInput.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/types/OnboardingHubState.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/__tests__/sendOnboardingCompleteEmail.test.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/queries/onboardingForms.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/queries/onboardingSchedulingUrl.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/queries/onboardingWelcomeCall.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/queries/__tests__/companyOnboardingSteps.test.js @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/queries/__tests__/onboardingWelcomeCall.test.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/queries/__tests__/onboardingSchedulingUrl.test.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/queries/__tests__/onboardingForms.test.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/queries/__tests__/onboardingForm.test.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/queries/onboardingForm.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/queries/companyOnboardingSteps.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/models/OnboardingStep.js @luxurypresence/flagship-launchx
/packages/api-gateway/src/models/CompanyOnboardingStep.js @luxurypresence/flagship-launchx
/packages/api-gateway/src/models/OnboardingForm.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/routes/v1/onboarding.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/services/OnboardingService.js @luxurypresence/flagship-launchx
/packages/api-gateway/src/services/OnboardingFormService.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/services/OnboardingIDxService.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/updateCrm.ts @luxurypresence/flagship-launchx
/packages/api-gateway/src/gql/mutations/__tests__/updateCrm.test.ts @luxurypresence/flagship-launchx

/packages/web-platform/src/scenes/SiteContent/CMT/ @luxurypresence/flagship-launchx
/packages/web-platform/src/scenes/Dashboard/Onboarding/ @luxurypresence/flagship-launchx
/packages/web-platform/src/scenes/Onboarding/ @luxurypresence/flagship-launchx
/packages/web-platform/src/gql/fragments/AgentForOnboardingFragment.graphql @luxurypresence/flagship-launchx
/packages/web-platform/src/gql/mutations/UpsertOnboardingForm.graphql @luxurypresence/flagship-launchx
/packages/web-platform/src/gql/mutations/UpdateCompanyOnboardingStep.graphql @luxurypresence/flagship-launchx
/packages/web-platform/src/gql/queries/CompanyOnboardingSteps.graphql @luxurypresence/flagship-launchx
/packages/web-platform/src/gql/queries/OnboardingForm.graphql @luxurypresence/flagship-launchx
/packages/web-platform/src/gql/queries/AgentsForOnboarding.graphql @luxurypresence/flagship-launchx
/packages/web-platform/src/gql/queries/CrawlJobsAndScrapeJobsCount.graphql @luxurypresence/flagship-launchx
/packages/web-platform/src/gql/queries/ScrapeJobs.graphql @luxurypresence/flagship-launchx
/packages/web-platform/src/components/OnboardingProgressBar/ @luxurypresence/flagship-launchx
/packages/web-platform/src/components/WebsiteProgress/ @luxurypresence/flagship-launchx
/packages/web-platform/src/hooks/onboarding/ @luxurypresence/flagship-launchx

/packages/cms-service/**/cmt* @luxurypresence/flagship-launchx
/packages/cms-service/**/Cmt* @luxurypresence/flagship-launchx
/packages/cms-service/sql/*cmt* @luxurypresence/flagship-launchx
/packages/cmt-web-scraper-lambda/ @luxurypresence/flagship-launchx
/packages/api-gateway/src/*cmt* @luxurypresence/flagship-launchx
/packages/api-gateway/src/*Cmt* @luxurypresence/flagship-launchx

# Shared between Platform and LaunchX
/packages/api-gateway/src/gql/mutations/updateCompany.ts @luxurypresence/flagship-platform @luxurypresence/flagship-launchx


# Explicit Platform
/packages/api-gateway/src/middleware/ @luxurypresence/flagship-platform

# client-marketing-seo
/packages/api-gateway/src/gql/seo/ @luxurypresence/client-marketing-seo
/packages/api-gateway/src/services/SeoRecommendationGroupService.ts @luxurypresence/client-marketing-seo
/packages/api-gateway/src/services/SeoRecommendationService.ts @luxurypresence/client-marketing-seo
/packages/web-platform/src/assets/client-marketing/ @luxurypresence/client-marketing-seo
/packages/web-platform/src/components/ad-performance-card.tsx @luxurypresence/client-marketing-seo
/packages/web-platform/src/components/ai-post-card.tsx @luxurypresence/client-marketing-seo
/packages/web-platform/src/components/blog-post-card.tsx @luxurypresence/client-marketing-seo
/packages/web-platform/src/components/client-marketing/ @luxurypresence/client-marketing-seo
/packages/web-platform/src/components/seo-hero-section.tsx @luxurypresence/client-marketing-seo
/packages/web-platform/src/components/seo-optimization-detail-card.tsx @luxurypresence/client-marketing-seo
/packages/web-platform/src/components/seo-recommendation-card.tsx @luxurypresence/client-marketing-seo
/packages/web-platform/src/gql/queries/HomepageFeed.router.graphql @luxurypresence/client-marketing-seo
/packages/web-platform/src/hooks/useHasLiveWebsite.ts @luxurypresence/client-marketing-seo
/packages/web-platform/src/scenes/Dashboard/dashboard-page.tsx @luxurypresence/client-marketing-seo
/packages/web-platform/src/scenes/Dashboard/home-feed/ @luxurypresence/client-marketing-seo
/packages/web-platform/src/scenes/Seo/ @luxurypresence/client-marketing-seo
/packages/web-platform/src/scenes/Reporting/ @luxurypresence/client-marketing-seo
/packages/web-platform/src/scenes/ClientReporting/ @luxurypresence/client-marketing-seo
/packages/web-platform/src/utils/__tests__/seo.test.ts @luxurypresence/client-marketing-seo
/packages/web-platform/src/utils/seo.ts @luxurypresence/client-marketing-seo

####################
# Shared Ownership #
####################

/packages/api-gateway/src/
/packages/web-platform/src/assets/
/packages/web-platform/src/gql/generated.ts
/packages/api-gateway/src/schema.graphql
/packages/web-platform/src/scenes/HomeSearch/Filters/ @luxurypresence/maps
/packages/web-platform/src/scenes/Settings/HomeSearch/Filters/ @luxurypresence/maps
/packages/web-platform/src/scenes/Lead/SavedSearch/ @luxurypresence/maps
/packages/web-platform/src/scenes/Leads/SavedSearchInAppEducation/ @luxurypresence/maps
/packages/web-platform/src/scenes/Admin/MlsProvider/Form/Filters.tsx @luxurypresence/maps
/packages/web-platform/src/scenes/Settings/HomeSearch/Filters/components/MlsProviderFilters.tsx @luxurypresence/maps
/packages/web-platform/src/scenes/Settings/HomeSearch/Filters/preview/ @luxurypresence/maps
/packages/web-platform/src/components/MultiPolygonInteractiveMap/** @luxurypresence/maps


#################
# Design System #
#################

/packages/web-platform/src/style/main.css @luxurypresence/design-system
/packages/web-platform/src/components/ui/** @luxurypresence/design-system
/packages/web-platform/components.json @luxurypresence/design-system
/packages/web-platform/src/components/page-header.tsx @luxurypresence/design-system
/packages/web-platform/src/components/page-layout.tsx @luxurypresence/design-system
/packages/web-platform/src/components/pagination-container.tsx @luxurypresence/design-system
/packages/web-platform/src/hooks/useAdminMenuBuilder.tsx @luxurypresence/design-system
/packages/web-platform/src/hooks/useMenuBuilder.tsx @luxurypresence/design-system
/packages/web-platform/src/hooks/useUserActions.ts @luxurypresence/design-system

=======

###########

/package.json
/packages/api-gateway/package.json
/packages/eslint-custom-config/package.json
/packages/lp-property-media-etl-loader/package.json
/packages/platform-tests-e2e/package.json
/packages/cms-service/package.json
/packages/web-platform/package.json
/pnpm-lock.yaml
/.changeset/
