# API Gateway Service Overview

## Purpose and Key Functionality
The API Gateway service acts as a Backend-for-Frontend (BFF) GraphQL API layer, providing a unified interface for frontend applications to interact with various backend services. It handles:

- Lead management and routing
- Property management and MLS integration
- Website configuration and management
- Network and company relationships
- Authentication and authorization

Evidence: 
- GraphQL schema definition: `src/schema.graphql`
- Core service implementations:
  - `src/services/NetworkService.ts`
  - `src/services/WebsiteService.ts`
  - `src/services/PropertyService.ts`
  - `src/services/crm/LeadService.ts`

## Role in System Architecture
The API Gateway serves as the central integration point between frontend applications and backend services:

1. Frontend Interface
   - Provides GraphQL API for frontend applications
   - Handles authentication and authorization
   - Manages request/response transformation

2. Backend Integration
   - Communicates with CRM service for lead management
   - Integrates with MLS providers for property data
   - Manages website configurations and deployments
   - Handles network and company relationships

3. Core Responsibilities
   - Request validation and transformation
   - Response formatting
   - Error handling and logging
   - Service orchestration
   - Data enrichment

Evidence:
- Service integrations: `src/services/CrmService.ts`
- Authentication middleware: `src/middleware/enforcers/`
- Lead management: `src/services/crm/LeadService.ts`

## Dependencies

### External Services
- CRM Service
- Website Service
- Property Service
- MLS Providers
- Network Service

### Infrastructure
- PostgreSQL Database
- Redis Cache
- AWS Services

Evidence:
- Database schemas: 
  - `sql/V2__network.sql`
  - `sql/V184__add_lead_tag_tables.sql`
- Service configurations: `src/config/development.json`

## Key Features

### Lead Management
- Lead creation and routing
- Lead enrichment
- Agent assignment
- Integration with external CRM systems

Evidence: `src/services/crm/LeadService.ts`

### Property Management
- Property listing management
- MLS integration
- Property search and filtering
- Media management

Evidence: `src/services/PropertyService.ts`

### Website Management
- Website configuration
- Template management
- Content management
- Deployment orchestration

Evidence: `src/services/WebsiteService.ts`

### Network Management
- Company network relationships
- Network-wide property sharing
- Access control

Evidence: `src/services/NetworkService.ts`

## Assumptions and Limitations
*Note: The following are explicitly labeled assumptions based on codebase evidence*

1. **Authentication Assumption**: The service assumes Auth0 as the primary authentication provider based on middleware implementation.
2. **Database Assumption**: PostgreSQL is assumed as the primary data store based on SQL migration files.
3. **Cache Assumption**: Redis is likely used for caching based on configuration files, though explicit cache implementations were not found in the codebase.

## Missing Information
*Note: The following information is not evident in the codebase*

1. Deployment configuration and infrastructure details
2. Monitoring and alerting setup
3. Performance metrics and SLAs
4. Disaster recovery procedures
5. Development environment setup instructions (though some dependencies are documented)
