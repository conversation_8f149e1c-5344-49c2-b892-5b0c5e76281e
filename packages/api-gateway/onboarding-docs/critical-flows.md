# Critical Operational Flows

## Lead Management Flow

### Lead Creation and Routing

```mermaid
sequenceDiagram
    participant Client
    participant GraphQL
    participant LeadService
    participant LeadEnrichment
    participant LeadRouting
    participant CRM
    participant Email

    Client->>GraphQL: addLead mutation
    GraphQL->>LeadService: create()
    LeadService->>LeadService: validate lead
    LeadService->>LeadService: check spam
    
    alt Is New Lead
        LeadService->>LeadEnrichment: enrich lead data
        LeadEnrichment-->>LeadService: enrichment data
        LeadService->>LeadRouting: get assigned agents
        LeadRouting-->>LeadService: agent assignments
        LeadService->>CRM: create lead record
        LeadService->>Email: notify agents
    else Existing Lead
        LeadService->>LeadService: update lead
        LeadService->>Email: notify on updates
    end
    
    LeadService-->>GraphQL: lead response
    GraphQL-->>Client: lead data
```

Evidence: `src/services/crm/LeadService.ts`

Key Components:
1. Lead Creation
2. Spam Detection
3. Lead Enrichment
4. Agent Assignment
5. Email Notifications

### Lead Routing Rules

```mermaid
flowchart TD
    A[New Lead] --> B{Check Rules}
    B -->|Form Rule| C[Match Form ID]
    B -->|Detail Page Rule| D[Match Page Type]
    B -->|Activity Type| E[Match Activity]
    
    C --> F{Assign Agents}
    D --> F
    E --> F
    
    F --> G[Create Lead Agent]
    G --> H[Send Notifications]
```

Evidence: `src/services/crm/LeadRoutingRuleService.ts`

## Property Management Flow

### Property Search and Filter

```mermaid
sequenceDiagram
    participant Client
    participant GraphQL
    participant PropertyService
    participant MLSService
    participant CMS

    Client->>GraphQL: properties query
    GraphQL->>PropertyService: find()
    
    alt Get CMS Listings
        PropertyService->>CMS: fetch properties
        CMS-->>PropertyService: CMS listings
    end
    
    alt Backfill MLS
        PropertyService->>MLSService: search listings
        MLSService-->>PropertyService: MLS listings
        PropertyService->>PropertyService: deduplicate listings
    end
    
    PropertyService-->>GraphQL: combined results
    GraphQL-->>Client: property data
```

Evidence: `src/services/PropertyService.ts`

Key Components:
1. Property Search
2. CMS Integration
3. MLS Backfill
4. Result Deduplication

## Website Management Flow

### Website Creation and Configuration

```mermaid
sequenceDiagram
    participant Client
    participant GraphQL
    participant WebsiteService
    participant MLSSearch
    participant Template

    Client->>GraphQL: createWebsite mutation
    GraphQL->>WebsiteService: create()
    WebsiteService->>Template: get template
    Template-->>WebsiteService: template config
    WebsiteService->>MLSSearch: createWebsite()
    MLSSearch-->>WebsiteService: MLS config
    WebsiteService-->>GraphQL: website data
    GraphQL-->>Client: response
```

Evidence: `src/services/WebsiteService.ts`

Key Components:
1. Website Creation
2. Template Integration
3. MLS Configuration
4. Website Update

## Network Management Flow

### Network Company Association

```mermaid
sequenceDiagram
    participant Client
    participant GraphQL
    participant NetworkService
    participant Database

    Client->>GraphQL: addCompanyNetwork mutation
    GraphQL->>NetworkService: addCompany()
    NetworkService->>Database: create association
    Database-->>NetworkService: confirmation
    NetworkService-->>GraphQL: result
    GraphQL-->>Client: response
```

Evidence: `src/services/NetworkService.ts`

Key Components:
1. Network Creation
2. Company Association
3. Network Validation

## Error Handling

### Common Error Patterns

```mermaid
flowchart TD
    A[Request] --> B{Validate}
    B -->|Invalid| C[Throw Error]
    B -->|Valid| D[Process]
    D --> E{Check Result}
    E -->|Error| F[Log Error]
    F --> G[Format Response]
    E -->|Success| H[Return Result]
```

Evidence:
- Error handling in LeadService: `src/services/crm/LeadService.ts`
- Network error handling: `src/services/NetworkService.ts`
- Property error handling: `src/services/PropertyService.ts`

## Missing Information
*Note: The following flows are not evident in the codebase*

1. User authentication flow details
2. Deployment and CI/CD flows
3. Data backup and recovery procedures
4. System monitoring and alerting flows
5. Performance optimization processes
