# Data Models

## Core Data Models

### Lead Management

```mermaid
erDiagram
    LEAD ||--o{ LEAD_TAG : has
    LEAD ||--o{ LEAD_AGENT : assigned
    LEAD ||--o{ LEAD_ENRICHMENT : enriched_by
    LEAD ||--o{ LEAD_ACTIVITY : tracks
    LEAD {
        uuid lead_id PK
        uuid company_id FK
        string email
        string first_name
        string last_name
        string phone_number
        string source
        string type
        boolean is_spam
        json access_control_layer
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    LEAD_TAG {
        uuid tag_id PK
        uuid lead_id FK
        timestamp created_at
    }
    LEAD_TAG_COMPANY {
        uuid tag_id PK
        uuid company_id FK
        string value
        string color
        string background_color
        int tag_order
    }
    LEAD_AGENT {
        uuid lead_id FK
        uuid agent_id FK
        timestamp created_at
        timestamp updated_at
    }
```

Evidence: `sql/V184__add_lead_tag_tables.sql`

### Network Management

```mermaid
erDiagram
    NETWORK ||--o{ COMPANY_NETWORK : contains
    NETWORK {
        uuid network_id PK
        string name
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    COMPANY_NETWORK {
        uuid company_id FK
        uuid network_id FK
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
```

Evidence: `sql/V2__network.sql`

## Data Model Details

### Lead Model
The Lead model represents potential clients and their interactions with the system.

#### Key Attributes
- `lead_id`: Primary identifier (UUID)
- `company_id`: Associated company (UUID)
- `email`: Contact email (required)
- `first_name`: First name (optional)
- `last_name`: Last name (optional)
- `phone_number`: Contact phone (optional)
- `source`: Lead source identifier
- `type`: Lead type classification
- `is_spam`: Spam flag
- `access_control_layer`: JSON field for permissions
- Timestamps: `created_at`, `updated_at`, `deleted_at`

Evidence: `src/services/crm/LeadService.ts`

#### Lead Tags
Lead tags provide categorization and status tracking:

Default Tags (Evidence: `sql/V184__add_lead_tag_tables.sql`):
- Buyer (blue)
- Seller (purple)
- Needs Attention (green)
- AI Managed (purple)
- Import (red)
- Spam (black)
- Unsubscribed (black)
- Do Not Contact (black)

### Network Model
Networks manage relationships between companies for resource sharing.

#### Key Attributes
- `network_id`: Primary identifier (UUID)
- `name`: Network name
- Timestamps: `created_at`, `updated_at`, `deleted_at`

Evidence: `sql/V2__network.sql`

#### Company Network Association
- Many-to-many relationship between companies and networks
- Tracks membership status and timestamps
- Enables resource sharing between companies

Evidence: `sql/V2__network.sql`

## Data Access Patterns

### Lead Management
1. Lead Creation
   ```typescript
   // Create lead with tags and agent assignments
   const lead = await Lead.create({
     leadId: uuid.v4(),
     isSpam: leadIsSpam,
     status: 'Lead',
     ...leadPayloadData
   });
   ```
   Evidence: `src/services/crm/LeadService.ts`

2. Lead Enrichment
   ```typescript
   // Update lead with enrichment data
   const enrichmentData = await LeadEnrichmentService.getEnrichment({
     leadId: leadRecord.leadId,
     email: leadRecord.email,
     firstName: leadRecord?.firstName,
     lastName: leadRecord?.lastName,
     phoneNumber: leadRecord?.phoneNumber
   });
   ```
   Evidence: `src/services/crm/LeadService.ts`

### Network Management
1. Network Creation
   ```typescript
   // Create network
   const network = await Network.create({
     networkId: uuid.v4(),
     name: networkName
   });
   ```
   Evidence: `src/services/NetworkService.ts`

2. Company Association
   ```typescript
   // Add company to network
   await request.post(`${NETWORK_SERVICE_URL}/api/v1/networks/${networkId}/companies/${companyId}`);
   ```
   Evidence: `src/services/NetworkService.ts`

## Data Validation

### Lead Validation
1. Required Fields
   - Email (lowercase enforced)
   - Company ID
   - Lead source

2. Spam Detection
   ```typescript
   const leadIsSpam = await LeadService.isSpam(
     leadPayloadData,
     leadType,
     content
   );
   ```
   Evidence: `src/services/crm/LeadService.ts`

### Network Validation
1. Required Fields
   - Network ID
   - Company ID (for associations)
   - Name

2. Unique Constraints
   - Company-Network associations must be unique
   Evidence: `sql/V2__network.sql`

## Missing Information
*Note: The following information is not evident in the codebase*

1. Complete database schema beyond leads and networks
2. Data archival policies
3. Data backup procedures
4. Data migration strategies
5. Performance optimization configurations
