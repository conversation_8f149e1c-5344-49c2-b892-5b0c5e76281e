# System Architecture

## Component Diagram

```mermaid
graph TB
    subgraph "API Gateway Service"
        GQL[GraphQL Layer]
        Auth[Authentication]
        Services[Service Layer]
        DataAccess[Data Access Layer]
    end

    subgraph "External Services"
        CRM[CRM Service]
        Website[Website Service]
        Property[Property Service]
        MLS[MLS Providers]
        Network[Network Service]
    end

    subgraph "Infrastructure"
        DB[(PostgreSQL)]
        Cache[(Redis)]
        AWS[AWS Services]
    end

    Client[Frontend Client] --> GQL
    GQL --> Auth
    Auth --> Services
    Services --> DataAccess
    DataAccess --> DB
    DataAccess --> Cache

    Services --> CRM
    Services --> Website
    Services --> Property
    Services --> MLS
    Services --> Network
    Services --> AWS

    classDef primary fill:#2757B6,stroke:#fff,stroke-width:2px,color:#fff
    classDef secondary fill:#A1237D,stroke:#fff,stroke-width:2px,color:#fff
    classDef external fill:#3A793C,stroke:#fff,stroke-width:2px,color:#fff

    class GQL,Auth,Services,DataAccess primary
    class CRM,Website,Property,MLS,Network secondary
    class DB,Cache,AWS external
```

Evidence:
- Service structure: `src/services/`
- External integrations: `src/services/CrmService.ts`, `src/services/WebsiteService.ts`
- Data access: SQL schemas in `sql/` directory

## Critical Integrations

### Lead Management Flow

```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant CRM Service
    participant Lead Enrichment
    participant Email Service

    Client->>API Gateway: Create Lead Mutation
    Note over API Gateway: Validate Request
    API Gateway->>CRM Service: Create Lead
    API Gateway->>Lead Enrichment: Enrich Lead Data
    Lead Enrichment-->>API Gateway: Enrichment Data
    API Gateway->>Email Service: Send Notifications
    API Gateway-->>Client: Lead Created Response
```

Evidence: 
- Lead creation: `src/services/crm/LeadService.ts`
- Lead enrichment: `src/services/crm/LeadEnrichmentService.ts`
- Lead routing: `src/services/crm/LeadRoutingRuleService.ts`

### Property Management Flow

```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant Property Service
    participant MLS Service
    participant Media Service

    Client->>API Gateway: Property Query
    API Gateway->>Property Service: Get Properties
    Property Service->>MLS Service: Fetch MLS Data
    MLS Service-->>Property Service: MLS Listings
    Property Service->>Media Service: Get Media
    Media Service-->>Property Service: Media Data
    Property Service-->>API Gateway: Combined Data
    API Gateway-->>Client: Property Response
```

Evidence:
- Property service: `src/services/PropertyService.ts`
- MLS integration: `src/services/mls/MlsSearchService/index.ts`

## Data Flow Patterns

### Authentication Flow

```mermaid
sequenceDiagram
    participant Client
    participant API Gateway
    participant Auth Middleware
    participant Permission Enforcer
    participant Service Layer

    Client->>API Gateway: GraphQL Request
    API Gateway->>Auth Middleware: Validate Token
    Auth Middleware->>Permission Enforcer: Check Permissions
    Permission Enforcer-->>Auth Middleware: Permission Result
    Auth Middleware-->>API Gateway: Auth Result
    API Gateway->>Service Layer: Execute Request
    Service Layer-->>Client: Response
```

Evidence:
- Auth middleware: `src/middleware/enforcers/enforcer.test.ts`
- Permission enforcement: `src/middleware/enforcers/`

## Infrastructure Components

### Database Schema Overview

```mermaid
erDiagram
    LEAD ||--o{ LEAD_TAG : has
    LEAD ||--o{ LEAD_AGENT : assigned
    LEAD {
        uuid lead_id
        uuid company_id
        string email
        string source
        timestamp created_at
    }
    LEAD_TAG {
        uuid tag_id
        uuid lead_id
        timestamp created_at
    }
    NETWORK ||--o{ COMPANY_NETWORK : contains
    NETWORK {
        uuid network_id
        string name
        timestamp created_at
    }
    COMPANY_NETWORK {
        uuid company_id
        uuid network_id
        timestamp created_at
    }
```

Evidence:
- Network schema: `sql/V2__network.sql`
- Lead tags schema: `sql/V184__add_lead_tag_tables.sql`

## Service Boundaries

The API Gateway maintains clear service boundaries through:

1. **GraphQL Layer**
   - Schema definition
   - Query/mutation resolution
   - Type validation

2. **Service Layer**
   - Business logic encapsulation
   - External service integration
   - Data transformation

3. **Data Access Layer**
   - Database interactions
   - Caching
   - Transaction management

Evidence:
- GraphQL schema: `src/schema.graphql`
- Service implementations: `src/services/`
- Data access: SQL schemas in `sql/` directory
