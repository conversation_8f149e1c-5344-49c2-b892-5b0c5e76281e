# API Documentation

## GraphQL API Overview

The API Gateway provides a GraphQL API that serves as the primary interface for frontend applications. This documentation covers key mutations and queries with their parameters and responses.

### Authentication and Authorization

All requests must include appropriate authentication headers. Authorization is handled through role-based access control.

Evidence: `src/middleware/enforcers/enforcer.test.ts`

## Core Mutations

### Lead Management

#### `addLead`
Creates a new lead in the system.

```graphql
mutation AddLead($lead: LeadInput!) {
  addLead(lead: $lead) {
    id
    firstName
    lastName
    phoneNumber
    email
    importSource
  }
}
```

Parameters:
- `lead`: Lead input object containing:
  - `firstName`: String (optional)
  - `lastName`: String (optional)
  - `phoneNumber`: String (optional)
  - `email`: String (required)

Response:
```json
{
  "data": {
    "addLead": {
      "id": "uuid",
      "firstName": "string",
      "lastName": "string",
      "phoneNumber": "string",
      "email": "string",
      "importSource": "string"
    }
  }
}
```

Evidence: 
- Schema: `src/schema.graphql`
- Implementation: `src/services/crm/LeadService.ts`
- Tests: `src/gql/crm/mutations/__tests__/addLead.test.ts`

#### `updateProperty`
Updates a property listing.

```graphql
mutation UpdateProperty($id: ID!, $input: PropertyInput!) {
  updateProperty(
    id: $id
    name: $input.name
    description: $input.description
    salesPrice: $input.salesPrice
    # ... other fields
  ) {
    id
    name
    description
    salesPrice
  }
}
```

Parameters:
- `id`: Property ID (required)
- Multiple optional fields for property details

Evidence: `src/schema.graphql`

### Website Management

#### `updateWebsite`
Updates website configuration.

```graphql
mutation UpdateWebsite($websiteId: ID!, $input: WebsiteInput!) {
  updateWebsite(
    websiteId: $websiteId
    name: $input.name
    hostname: $input.hostname
    # ... other fields
  ) {
    websiteId
    name
    hostname
  }
}
```

Parameters:
- `websiteId`: Website ID (required)
- Multiple optional configuration fields

Evidence: `src/schema.graphql`

## Core Queries

### Property Queries

#### `properties`
Fetches property listings with filtering options.

```graphql
query Properties($filter: PropertyFilter) {
  properties(
    companyId: $filter.companyId
    statusIds: $filter.statusIds
    salesPriceGTE: $filter.minPrice
    salesPriceLTE: $filter.maxPrice
    # ... other filters
  ) {
    id
    name
    description
    salesPrice
    # ... other fields
  }
}
```

Parameters:
- Multiple optional filter parameters
- Pagination options
- Sorting options

Evidence: `src/schema.graphql`

### Network Queries

#### `networks`
Fetches available networks.

```graphql
query Networks($filter: NetworkFilter) {
  networks(filter: $filter) {
    id
    name
    companies {
      id
      name
    }
  }
}
```

Evidence: `src/services/NetworkService.ts`

## Error Handling

### Error Types
1. Authentication Errors
   - 401: Unauthorized
   - 403: Forbidden

2. Validation Errors
   - Invalid input parameters
   - Missing required fields

3. Business Logic Errors
   - Resource not found
   - Operation not allowed

Example Error Response:
```json
{
  "errors": [
    {
      "message": "Cannot save lead, companyId not specified.",
      "locations": [{"line": 2, "column": 3}],
      "path": ["addLead"]
    }
  ]
}
```

Evidence: `src/services/crm/LeadService.ts`

## External Service Contracts

### CRM Service Integration
- Endpoint: Configured in environment
- Authentication: API Key
- Purpose: Lead management and enrichment

Evidence: `src/services/CrmService.ts`

### MLS Integration
- Purpose: Property listing data
- Authentication: Provider-specific
- Data synchronization

Evidence: `src/services/mls/MlsSearchService/index.ts`

## Rate Limiting and Quotas

*Note: Not evident in codebase*
The following aspects of API management are not documented in the code:
1. Rate limiting configuration
2. Usage quotas
3. Throttling policies

## API Versioning

*Note: Based on evidence in codebase*
- API versioning is handled through the GraphQL schema
- Breaking changes require schema updates
- Clients specify required fields in queries

## Missing Information
*Note: The following information is not evident in the codebase*

1. Complete API documentation for all mutations/queries
2. Performance SLAs
3. Rate limiting policies
4. API deprecation policies
5. Complete error code documentation
