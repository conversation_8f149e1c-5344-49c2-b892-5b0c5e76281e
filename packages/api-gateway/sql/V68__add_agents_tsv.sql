ALTER TABLE agent ADD COLUMN tsv TSVECTOR;

CREATE INDEX agent_search_idx ON agent USING gin(tsv);

UPDATE agent SET tsv = to_tsvector('pg_catalog.english', CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, ''))) ||
    to_tsvector('pg_catalog.simple', CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')));

CREATE FUNCTION agents_tsv_update_trigger() RETURNS trigger AS $$
begin
  new.tsv :=
    to_tsvector('pg_catalog.english', CONCAT(COALESCE(new.first_name, ''), ' ', COALESCE(new.last_name, ''))) ||
    to_tsvector('pg_catalog.simple', CONCAT(COALESCE(new.first_name, ''), ' ', COALESCE(new.last_name, '')));
  return new;
end
$$ LANGUAGE plpgsql;

CREATE TRIGGER agent_tsvectorupdate BEFORE INSERT OR UPDATE 
ON agent FOR EACH ROW EXECUTE PROCEDURE agents_tsv_update_trigger();
