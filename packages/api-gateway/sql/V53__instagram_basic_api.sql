CREATE TYPE instagram_authorization_api_type AS ENUM (
    'GRAPH',
    'INSTAGRAM'
);

ALTER TABLE instagram_authorization
  ADD COLUMN api_type instagram_authorization_api_type DEFAULT 'GRAPH'::instagram_authorization_api_type NOT NULL;

DROP TABLE instagram_feed_cache;

CREATE TABLE instagram_feed_cache (
    username text NOT NULL,
    api_type instagram_authorization_api_type DEFAULT 'GRAPH'::instagram_authorization_api_type NOT NULL,
    "limit" integer NOT NULL,
    content jsonb DEFAULT '{}'::jsonb NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    PRIMARY KEY (username, api_type, "limit")
);
