INSERT INTO permission VALUES ('a3b3f242-f6ab-443e-bbbe-bcdcede092cb', 'INTEGRATION', 'READ', NOW(), NOW(), NULL);
INSERT INTO permission VALUES ('8e827e45-05f3-49d3-8dc1-bc26bdf3e19b', 'INTEGRATION', 'WRITE', NOW(), NOW(), NULL);

INSERT INTO role_permission VALUES ('a3b3f242-f6ab-443e-bbbe-bcdcede092cb', '2cfd16b9-c55e-4fbe-aca0-02ffcb2b3b7b', NOW(), NOW(), NULL); -- READ, PRO
INSERT INTO role_permission VALUES ('a3b3f242-f6ab-443e-bbbe-bcdcede092cb', '8998af93-443c-49e3-9237-634583a947af', NOW(), NOW(), NULL); -- READ, ADMI<PERSON>
INSERT INTO role_permission VALUES ('8e827e45-05f3-49d3-8dc1-bc26bdf3e19b', '2cfd16b9-c55e-4fbe-aca0-02ffcb2b3b7b', NOW(), NOW(), NULL); -- WRITE, PRO
INSERT INTO role_permission VALUES ('8e827e45-05f3-49d3-8dc1-bc26bdf3e19b', '8998af93-443c-49e3-9237-634583a947af', NOW(), NOW(), NULL); -- WRITE, ADMIN

CREATE TYPE integration_provider AS ENUM (
    'INCOMPLETE',
    'CONTACTUALLY',
    'FOLLOWUPBOSS',
    'SENDGRID_CONTACTS'
);

CREATE TABLE integration (
  integration_id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
  company_id uuid NOT NULL,
  name text,
  provider integration_provider DEFAULT 'INCOMPLETE'::integration_provider NOT NULL,
  api_token text NOT NULL,
  created_at timestamp with time zone DEFAULT now() NOT NULL,
  updated_at timestamp with time zone DEFAULT now() NOT NULL,
  deleted_at timestamp with time zone,
  CONSTRAINT integration_pkey PRIMARY KEY (integration_id),
  CONSTRAINT integration_company_id_fkey FOREIGN KEY (company_id) REFERENCES company(company_id)
);

CREATE INDEX index_integration_company_id ON integration (company_id);

ALTER TABLE hook
  ADD COLUMN integration_id uuid,
  ADD COLUMN title text,
  ADD COLUMN deleted_at timestamp with time zone,
  ADD CONSTRAINT hook_integration_id_fkey FOREIGN KEY (integration_id) REFERENCES integration(integration_id);

CREATE INDEX index_hook_integration_integration_id ON hook (integration_id);
