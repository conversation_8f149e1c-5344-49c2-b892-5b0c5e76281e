UPDATE testimonial
SET display_order = new_order.new_display_order
FROM
  (SELECT t.testimonial_id AS testimonialId, t.company_id as companyId,
          ROW_NUMBER() OVER (PARTITION BY t.company_id
                             ORDER BY t.created_at DESC) AS new_display_order
   FROM testimonial t
   WHERE t.deleted_at IS NULL ) AS new_order
WHERE testimonial_id = new_order.testimonialId
AND company_id = new_order.companyId
AND deleted_at IS NULL;