import DataAccess from '../src/utils/DataAccess';

const COMPANY_ID = '445ac473-967a-4a50-9f8a-35b5a81c5233';

// 6653feb4-f3f6-4e5c-aa22-8cd5420f9702
// this company has records with up to 3k display order
const [_, __, modelName, pkFieldName, companyId] = process.argv;
/**
 * Generic script for fixing duplicated display orders on CMS
 * @param {Object} param0 required parameters
 * @return {void}
 */
const fixDisplayOrders = async (modelName, pkFieldName, companyId) => {
  console.log('Fixing display orders for', modelName, companyId, pkFieldName);
  const companyTotalCount = await DataAccess[modelName].count({
    where: {
      companyId,
    },
  });
  const biggestOrder = await DataAccess[modelName].findAll({
    where: { companyId },
    attributes: [
      DataAccess.sequelize.fn('max', DataAccess.sequelize.col('display_order')),
    ],
    raw: true,
  });
  const maxDisplayOrder = biggestOrder?.[0].max ?? 0;
  console.log(
    `Company ${companyId} has ${companyTotalCount} records, biggest display order is ${maxDisplayOrder}`,
  );
  const duplicatedDisplayOrderRecords = [];
  for (let i = 1; i <= maxDisplayOrder; i++) {
    const duplicatedOrder = await DataAccess[modelName].findAll({
      attributes: [pkFieldName],
      where: {
        companyId,
        displayOrder: i,
      },
    });
    if (duplicatedOrder?.length > 1) {
      // @ts-ignore
      duplicatedDisplayOrderRecords.push(...duplicatedOrder);
    }
  }
  duplicatedDisplayOrderRecords.map(async (id, idx) => {
    await DataAccess[modelName].update(
      {
        displayOrder: biggestOrder + idx + 1,
      },
      {
        where: {
          companyId,
          [pkFieldName]: id,
        },
      },
    );
  });
};

fixDisplayOrders(modelName, pkFieldName, companyId || COMPANY_ID);
