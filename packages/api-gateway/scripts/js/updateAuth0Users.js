/* eslint-disable no-console */
require('app-module-path').addPath(`${__dirname}/../..`);
const UserService = require('src/services/UserService');
const Auth0 = require('src/utils/Auth0');

/**
 * How to run this script for prod:
 * - set the identityService.host to the tenant-service.luxurypresence.com in your local.json config
 * - set the auth0.domain to the production domain
 * - set the auth0.adminApplication.clientId and auth0.adminApplication.clientSecret to the production client id and client secret
 * - run the script with: npx ts-node packages/api-gateway/scripts/js/updateAuth0Users.js
 */

const auth0 = new Auth0();
const BATCH_LIMIT = 10;

const AUTH0_RPM_MAX = 75;
let auth0FirstBatchRequests = new Date(); // Timestamp of first request, reset every minute
let auth0RequestCounter = 0;

/**
 * Patch Auth0 User Object metadata
 * Auth0 implements rate limiting of 20rps and 200rpm to this endpoint
 * Ensure we only take 25% of that capacity
 * Meant to be called synchronously
 * @param {string} id
 * @param {Object} userObj
 * @return {Promise<boolean>}
 */
const patchAuth0 = async (id, userObj) => {
  if (auth0RequestCounter >= AUTH0_RPM_MAX) {
    const delay =
      60 * 1000 - (new Date().getTime() - auth0FirstBatchRequests.getTime());
    console.log(`${AUTH0_RPM_MAX} Auth0 RPM reached.. delaying ${delay}ms`);
    await new Promise(resolve => setTimeout(resolve, delay));
    auth0RequestCounter = 0;
    auth0FirstBatchRequests = new Date();
  }

  try {
    console.log(JSON.stringify(userObj, null, 2));
    await auth0.updateUser(id, userObj);
  } catch (e) {
    if (e.response) {
      console.log(`AUTH0 ERROR: ${e.response.text}`);
    } else {
      console.log(`FATAL ERROR: ${e.message}`);
    }
  }
  auth0RequestCounter++;
  return true;
};

/**
 * Fetch and Update a batch of users on auth0
 * @param {number} offset
 * @return {Promise<{hasMore: boolean, processed: number, updated: number, skipped: number, errors: number}>}
 */
const updateUserBatch = async offset => {
  console.log(`Fetching users from offset ${offset}`);
  const users = await UserService.find(
    {},
    {
      offset,
      limit: BATCH_LIMIT,
      sort: 'created_at',
      sortDir: 'asc',
      include: ['companies'],
    },
  );

  console.log(`Fetched ${users.length} users from offset ${offset}`);
  for (let i = 0; i < users.length; i++) {
    const user = users[i];
    if (user.externalAuthId) {
      try {
        const companyRoleMap = {};
        user.companies.forEach(c => {
          if (c.companyId && c.roleId) {
            companyRoleMap[c.companyId] = {
              roleId: c.roleId,
              ...(c.agentId && { agentId: c.agentId }),
            };
          }
        });

        const { auth0UserObj } = UserService.toAuth0Fields(
          { email: user.email },
          user,
          companyRoleMap,
        );

        console.log(`Updating user ${user.externalAuthId} with ${Object.keys(companyRoleMap).length} company roles`);
        await patchAuth0(user.externalAuthId, auth0UserObj);
      } catch (error) {
        console.error(`Error processing user ${user.externalAuthId}:`, error.message);
      }
    } else {
      console.log(`Skipping user ${user.userId || user.displayId} - no Auth0 external ID`);
    }
  }

  return {
    hasMore: users.length === BATCH_LIMIT,
    processed: users.length,
    updated: users.filter(u => u.externalAuthId && u.companies?.length > 0).length,
    skipped: users.filter(u => !u.externalAuthId || !u.companies?.length).length,
    errors: 0,
  };
};

/**
 * Execute script to update Auth0 with latest user metadata
 * @return {Promise<void>}
 */
const updateAuth0Users = async () => {
  console.log('Starting Auth0 user metadata sync...');

  let offset = 0;
  let hasMore = true;
  let totalProcessed = 0;
  let totalUpdated = 0;
  let totalSkipped = 0;
  let totalErrors = 0;

  while (hasMore) {
    try {
      const result = await updateUserBatch(offset);
      hasMore = result.hasMore;
      totalProcessed += result.processed;
      totalUpdated += result.updated;
      totalSkipped += result.skipped;
      totalErrors += result.errors;
      offset += BATCH_LIMIT;
    } catch (error) {
      console.error(`Fatal error processing batch at offset ${offset}:`, error);
      break;
    }
  }
  
  console.log('Sync completed!');
  console.log(`Total processed: ${totalProcessed}`);
  console.log(`Total updated: ${totalUpdated}`);
  console.log(`Total skipped: ${totalSkipped}`);
  console.log(`Total errors: ${totalErrors}`);
};

updateAuth0Users().catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
