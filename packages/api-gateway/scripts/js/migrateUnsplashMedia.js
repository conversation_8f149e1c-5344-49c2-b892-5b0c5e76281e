require('app-module-path').addPath(`${__dirname}/../..`);

const { Op } = require('sequelize');
const DataAccess = require('src/utils/DataAccess');

const LIMIT = 1000;

// eslint-disable-next-line require-jsdoc
const migrateUnsplashMedia = async () => {
  try {
    let media = [];
    let hasMore = true;

    while (hasMore) {
      const mediaRecords = await DataAccess.MediaModel.findAll({
        where: {
          sourceName: 'Unsplash',
          smallUrl: {
            [Op.like]: `%&w=400%`,
          },
        },
        limit: LIMIT,
        offset: media.length,
      });

      const mediaObjs = mediaRecords.map(r => {
        const data = r.get();
        const newSmallUrl = data.smallUrl.replace('&w=400', '&w=960');
        const newMediumUrl = data.mediumUrl.replace('&w=1080', '&w=1280');

        return {
          ...data,
          smallUrl: newSmallUrl,
          mediumUrl: newMediumUrl,
        };
      });

      media = [...media, ...mediaObjs];
      hasMore = mediaRecords.length === LIMIT;
    }

    // bulk update media records
    for (let i = 0; i < media.length; i += LIMIT) {
      const chunk = media.slice(i, i + LIMIT);
      await DataAccess.MediaModel.bulkCreate(chunk, {
        updateOnDuplicate: ['smallUrl', 'mediumUrl'],
      });
    }

    // eslint-disable-next-line no-console
    console.log(`Unsplash media objects updated: ${media.length}`);
  } catch (e) {
    // eslint-disable-next-line no-console
    console.log('e', e);
  }
};

migrateUnsplashMedia();
