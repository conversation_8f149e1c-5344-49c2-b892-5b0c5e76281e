/* eslint-disable no-console */
require('app-module-path').addPath(`${__dirname}/../..`);
const Auth0 = require('src/utils/Auth0');

const auth0 = new Auth0();
const BATCH_LIMIT = 100;

/**
 * Fetch and Update a batch of users on auth0
 * @param {number} offset
 * @return {Promise<boolean>}
 */
const deleteNonClientMFA = async () => {
  // TODO: Make this a parameter than can be passed in via CLI
  let jobId;
  if (!jobId) {
    console.log(`Generating export job`);
    const result = await auth0.exportUsers({
      fields: [
        {
          'name':'user_id'
        },
        {
          'name':'email'
        },
        {
          'name':'user_metadata'
        },
        {
          'name':'multifactor'
        }
      ],
      format: 'json',
    });
    console.log('Job created, waiting for completion');
    console.log(`Job ID: ${result.id}`);
    jobId = result.id;
  } 

  let exportJob;
  do {
    exportJob = await auth0.checkExportJob(jobId);
    if (exportJob.status !== 'completed') {
      console.log('Job still processing');
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  } while (exportJob.status !== 'completed');
  const { location } = exportJob;
  if (!location) {
    console.log('Job failed');
    return;
  }
  const users = await auth0.downloadExportJob(location);
  console.log(`Fetched ${users.length} users from export`);
  for (const userString of users) {
    try {
      const user = JSON.parse(userString);
      console.log(userString);
      const { user_metadata } = user as { user_metadata: Record<string, any> };
      if (user_metadata?.type !== 'CLIENT') {
        continue;
      }
      const { multifactor } = user as { multifactor: string[] };
      if (!multifactor?.length) {
        continue;
      }
      for (const mfa of multifactor) {
        if (mfa.toLowerCase() === 'guardian') {
          const guardian_authenticators = await auth0.getAuthenticators(user.user_id);
          for (const authenticator of guardian_authenticators) {
            await auth0.deleteAuthenticator(user.user_id, authenticator.id);
          }
        }
      }
    } catch (err) {
      console.log('Encountered error with user', err, userString);
    }
  }

  return;
}

/**
 * Execute script to update Auth0 with latest user metadata
 * @return {Promise<void>}
 */
const updateAuth0Users = async () => {
  console.log('Starting...');
  await deleteNonClientMFA();
  console.log('Finished');
};

updateAuth0Users();
