require('app-module-path').addPath(`${__dirname}/../..`);

const MediaService = require('src/services/MediaService');

const iterations = 1000;

const createUrls = () => {
  const urls = []
  for (let i = 0; i < iterations; i++) {
    urls.push(`https://luxurypresence.com/${iterations}/test.jpg`);
  }
  return urls;
}

const run1 = async () => {
  const urls = createUrls();
  const start = new Date().getTime();
  for (let i = 0; i < iterations; i++) {
    MediaService.build([urls[i]]);
    //console.log(record)
  }
  const end = new Date().getTime();
  const duration = end - start;
  console.log(`Duration: ${duration}ms`);
}

const run2 = async () => {
  const urls = createUrls()

  const start = new Date().getTime();
  const records = MediaService.build(urls);
  //console.log(records);
  const end = new Date().getTime();
  const duration = end - start;
  console.log(`Duration: ${duration}ms`);
}

const run = async () => {
  console.log('Run 1');
  await run1();
  console.log('Run 2');
  await run2();
}
run().catch(console.error);
