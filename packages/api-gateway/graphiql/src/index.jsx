import GraphiQL from 'graphiql';
import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom/client';
import { Auth0Provider, useAuth0 } from '@auth0/auth0-react';
import superagent from 'superagent';

import 'graphiql/graphiql.css';

const AUTH0_DOMAIN = window.luxuryPresence.config.auth0Domain;
const AUTH0_GRAPHIQL_CLIENT_ID = window.luxuryPresence.config.auth0ClientId;

const App = () => {
  const {
    isLoading,
    isAuthenticated,
    error,
    getAccessTokenSilently,
    loginWithRedirect,
    logout,
  } = useAuth0();
  const [token, setToken] = useState(null);

  if (window.location.pathname.startsWith('/graphiql/logout')) {
    logout({
      returnTo: `${window.location.origin}/graphiql`
    });
    return null;
  }

  useEffect(() => {
    (async () => {
      try {
        const token = await getAccessTokenSilently({
          audience: window.location.origin,
        });
        setToken(token);
      } catch (e) {
        console.error(e);
      }
    })();
  }, [getAccessTokenSilently]);

  const fetcher = async (graphQLParams, opts) => {
    const { body } = await superagent.post('/graphql')
      .set('Authorization', `Bearer ${token}`)
      .send(graphQLParams);

    return body;
  }

  if (isLoading) {
    return 'Loading';
  } else if (error) {
    console.log(error);
    return error;
  } else if (!isAuthenticated) {
    loginWithRedirect();
    return 'Redirecting...';
  }

  if (!token) {
    return null;
  }

  return (
    <GraphiQL fetcher={fetcher} />
  )
}

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(<Auth0Provider
  domain={AUTH0_DOMAIN}
  clientId={AUTH0_GRAPHIQL_CLIENT_ID}
  redirectUri={`${window.location.origin}/graphiql`}
>
  <App />
</Auth0Provider>);
